<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Lexend%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-white justify-between group/design-root overflow-x-hidden" style='font-family: Lexend, "Noto Sans", sans-serif;'>
      <div>
        <div class="flex items-center bg-white p-4 pb-2 justify-between">
          <div class="text-[#121416] flex size-12 shrink-0 items-center" data-icon="ArrowLeft" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"></path>
            </svg>
          </div>
          <h2 class="text-[#121416] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">فصل‌ها</h2>
        </div>
        <h3 class="text-[#121416] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Course: Mathematics</h3>
        <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 justify-between">
          <div class="flex flex-col justify-center">
            <p class="text-[#121416] text-base font-medium leading-normal line-clamp-1">Algebra</p>
            <p class="text-[#6a7581] text-sm font-normal leading-normal line-clamp-2">10 questions</p>
          </div>
          <div class="shrink-0">
            <div class="text-[#121416] flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
              </svg>
            </div>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 justify-between">
          <div class="flex flex-col justify-center">
            <p class="text-[#121416] text-base font-medium leading-normal line-clamp-1">Calculus</p>
            <p class="text-[#6a7581] text-sm font-normal leading-normal line-clamp-2">15 questions</p>
          </div>
          <div class="shrink-0">
            <div class="text-[#121416] flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
              </svg>
            </div>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 justify-between">
          <div class="flex flex-col justify-center">
            <p class="text-[#121416] text-base font-medium leading-normal line-clamp-1">Geometry</p>
            <p class="text-[#6a7581] text-sm font-normal leading-normal line-clamp-2">8 questions</p>
          </div>
          <div class="shrink-0">
            <div class="text-[#121416] flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
              </svg>
            </div>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 justify-between">
          <div class="flex flex-col justify-center">
            <p class="text-[#121416] text-base font-medium leading-normal line-clamp-1">Trigonometry</p>
            <p class="text-[#6a7581] text-sm font-normal leading-normal line-clamp-2">12 questions</p>
          </div>
          <div class="shrink-0">
            <div class="text-[#121416] flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
              </svg>
            </div>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 justify-between">
          <div class="flex flex-col justify-center">
            <p class="text-[#121416] text-base font-medium leading-normal line-clamp-1">Statistics</p>
            <p class="text-[#6a7581] text-sm font-normal leading-normal line-clamp-2">5 questions</p>
          </div>
          <div class="shrink-0">
            <div class="text-[#121416] flex size-7 items-center justify-center" data-icon="CaretRight" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="flex justify-end overflow-hidden px-5 pb-5">
          <button
            class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-14 bg-[#b2cae5] text-[#121416] text-base font-bold leading-normal tracking-[0.015em] min-w-0 px-2 gap-4 pl-4 pr-6"
          >
            <div class="text-[#121416]" data-icon="Plus" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M224,128a8,8,0,0,1-8,8H136v80a8,8,0,0,1-16,0V136H40a8,8,0,0,1,0-16h80V40a8,8,0,0,1,16,0v80h80A8,8,0,0,1,224,128Z"></path>
              </svg>
            </div>
          </button>
        </div>
        <div class="h-5 bg-white"></div>
      </div>
    </div>
  </body>
</html>
