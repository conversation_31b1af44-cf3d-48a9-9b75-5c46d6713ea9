<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Lexend%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div
      class="relative flex size-full min-h-screen flex-col bg-white justify-between group/design-root overflow-x-hidden"
      style="--checkbox-tick-svg: url('data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27rgb(18,20,22)%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e'); font-family: Lexend, &quot;Noto Sans&quot;, sans-serif;"
    >
      <div>
        <div class="flex items-center bg-white p-4 pb-2 justify-between">
          <div class="text-[#121416] flex size-12 shrink-0 items-center" data-icon="ArrowLeft" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"></path>
            </svg>
          </div>
          <h2 class="text-[#121416] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Options</h2>
        </div>
        <h3 class="text-[#121416] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Question 1</h3>
        <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 justify-between">
          <div class="flex flex-col justify-center">
            <p class="text-[#121416] text-base font-medium leading-normal line-clamp-1">Option A</p>
            <p class="text-[#6a7581] text-sm font-normal leading-normal line-clamp-2">Option 1</p>
          </div>
          <div class="shrink-0">
            <button
              class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-8 px-4 bg-[#f1f2f4] text-[#121416] text-sm font-medium leading-normal w-fit"
            >
              <span class="truncate">Button</span>
            </button>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 justify-between">
          <div class="flex flex-col justify-center">
            <p class="text-[#121416] text-base font-medium leading-normal line-clamp-1">Option B</p>
            <p class="text-[#6a7581] text-sm font-normal leading-normal line-clamp-2">Option 2</p>
          </div>
          <div class="shrink-0">
            <div class="flex size-7 items-center justify-center">
              <input
                type="checkbox"
                class="h-5 w-5 rounded border-[#dde0e3] border-2 bg-transparent text-[#b2cae5] checked:bg-[#b2cae5] checked:border-[#b2cae5] checked:bg-[image:--checkbox-tick-svg] focus:ring-0 focus:ring-offset-0 focus:border-[#dde0e3] focus:outline-none"
              />
            </div>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 justify-between">
          <div class="flex flex-col justify-center">
            <p class="text-[#121416] text-base font-medium leading-normal line-clamp-1">Option C</p>
            <p class="text-[#6a7581] text-sm font-normal leading-normal line-clamp-2">Option 3</p>
          </div>
          <div class="shrink-0">
            <div class="flex size-7 items-center justify-center">
              <input
                type="checkbox"
                class="h-5 w-5 rounded border-[#dde0e3] border-2 bg-transparent text-[#b2cae5] checked:bg-[#b2cae5] checked:border-[#b2cae5] checked:bg-[image:--checkbox-tick-svg] focus:ring-0 focus:ring-offset-0 focus:border-[#dde0e3] focus:outline-none"
              />
            </div>
          </div>
        </div>
        <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 justify-between">
          <div class="flex flex-col justify-center">
            <p class="text-[#121416] text-base font-medium leading-normal line-clamp-1">Option D</p>
            <p class="text-[#6a7581] text-sm font-normal leading-normal line-clamp-2">Option 4</p>
          </div>
          <div class="shrink-0">
            <div class="flex size-7 items-center justify-center">
              <input
                type="checkbox"
                class="h-5 w-5 rounded border-[#dde0e3] border-2 bg-transparent text-[#b2cae5] checked:bg-[#b2cae5] checked:border-[#b2cae5] checked:bg-[image:--checkbox-tick-svg] focus:ring-0 focus:ring-offset-0 focus:border-[#dde0e3] focus:outline-none"
              />
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="flex justify-stretch">
          <div class="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-between">
            <button
              class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#f1f2f4] text-[#121416] text-sm font-bold leading-normal tracking-[0.015em]"
            >
              <span class="truncate">Add Option</span>
            </button>
            <button
              class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#b2cae5] text-[#121416] text-sm font-bold leading-normal tracking-[0.015em]"
            >
              <span class="truncate">Save</span>
            </button>
          </div>
        </div>
        <div class="h-5 bg-white"></div>
      </div>
    </div>
  </body>
</html>
