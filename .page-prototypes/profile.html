<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Lexend%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div class="relative flex size-full min-h-screen flex-col bg-white justify-between group/design-root overflow-x-hidden" style='font-family: Lexend, "Noto Sans", sans-serif;'>
      <div>
        <div class="flex items-center bg-white p-4 pb-2 justify-between">
          <div class="text-[#121416] flex size-12 shrink-0 items-center" data-icon="ArrowLeft" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"></path>
            </svg>
          </div>
          <h2 class="text-[#121416] text-lg font-bold leading-tight tracking-[-0.015em] flex-1 text-center pr-12">Profile</h2>
        </div>
        <div class="flex p-4 @container">
          <div class="flex w-full flex-col gap-4 items-center">
            <div class="flex gap-4 flex-col items-center">
              <div
                class="bg-center bg-no-repeat aspect-square bg-cover rounded-full min-h-32 w-32"
                style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuBmAEqOcIYS4ohTj-Ed3MhNPl_BDMHc4uQtrH2Ra5rR-JJDtlHUsjUlWWEThg6jiLRQ8SKd4jHgo5AwmtMAvFuRMdz3yDFhoeWX32Dit7oBaS1EMf1DvP4tv0IwL8ixOHs3EKqTwPRx2kUPb3i2md1DFtUQIbbVA3oiXqABVUxsDGbRqDxee8qdAIYPwNLzCuObxkk9EzXSWCPGpYpcE_H_ENFcpeO18Cxy4E4g42Lf4rtMZYz7Qqhc0QaokdysaBvbiWv225EikMEO");'
              ></div>
              <div class="flex flex-col items-center justify-center justify-center">
                <p class="text-[#121416] text-[22px] font-bold leading-tight tracking-[-0.015em] text-center">Ethan Carter</p>
                <p class="text-[#6a7581] text-base font-normal leading-normal text-center"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
        <h3 class="text-[#121416] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Account</h3>
        <div class="flex items-center gap-4 bg-white px-4 min-h-14">
          <div class="text-[#121416] flex items-center justify-center rounded-lg bg-[#f1f2f4] shrink-0 size-10" data-icon="User" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M230.92,212c-15.23-26.33-38.7-45.21-66.09-54.16a72,72,0,1,0-73.66,0C63.78,166.78,40.31,185.66,25.08,212a8,8,0,1,0,13.85,8c18.84-32.56,52.14-52,89.07-52s70.23,19.44,89.07,52a8,8,0,1,0,13.85-8ZM72,96a56,56,0,1,1,56,56A56.06,56.06,0,0,1,72,96Z"
              ></path>
            </svg>
          </div>
          <p class="text-[#121416] text-base font-normal leading-normal flex-1 truncate">Edit Profile</p>
        </div>
        <div class="flex items-center gap-4 bg-white px-4 min-h-14">
          <div class="text-[#121416] flex items-center justify-center rounded-lg bg-[#f1f2f4] shrink-0 size-10" data-icon="Lock" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M208,80H176V56a48,48,0,0,0-96,0V80H48A16,16,0,0,0,32,96V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V96A16,16,0,0,0,208,80ZM96,56a32,32,0,0,1,64,0V80H96ZM208,208H48V96H208V208Zm-68-56a12,12,0,1,1-12-12A12,12,0,0,1,140,152Z"
              ></path>
            </svg>
          </div>
          <p class="text-[#121416] text-base font-normal leading-normal flex-1 truncate">Change Password</p>
        </div>
        <div class="flex items-center gap-4 bg-white px-4 min-h-14">
          <div class="text-[#121416] flex items-center justify-center rounded-lg bg-[#f1f2f4] shrink-0 size-10" data-icon="Bell" data-size="24px" data-weight="regular">
            <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
              <path
                d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-43.92,16-80a64,64,0,1,1,128,0c0,36.05,8.28,66.73,16,80Z"
              ></path>
            </svg>
          </div>
          <p class="text-[#121416] text-base font-normal leading-normal flex-1 truncate">Notifications</p>
        </div>
        <h3 class="text-[#121416] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Settings</h3>
        <div class="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
          <div class="flex items-center gap-4">
            <div class="text-[#121416] flex items-center justify-center rounded-lg bg-[#f1f2f4] shrink-0 size-10" data-icon="Globe" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24ZM101.63,168h52.74C149,186.34,140,202.87,128,215.89,116,202.87,107,186.34,101.63,168ZM98,152a145.72,145.72,0,0,1,0-48h60a145.72,145.72,0,0,1,0,48ZM40,128a87.61,87.61,0,0,1,3.33-24H81.79a161.79,161.79,0,0,0,0,48H43.33A87.61,87.61,0,0,1,40,128ZM154.37,88H101.63C107,69.66,116,53.13,128,40.11,140,53.13,149,69.66,154.37,88Zm19.84,16h38.46a88.15,88.15,0,0,1,0,48H174.21a161.79,161.79,0,0,0,0-48Zm32.16-16H170.94a142.39,142.39,0,0,0-20.26-45A88.37,88.37,0,0,1,206.37,88ZM105.32,43A142.39,142.39,0,0,0,85.06,88H49.63A88.37,88.37,0,0,1,105.32,43ZM49.63,168H85.06a142.39,142.39,0,0,0,20.26,45A88.37,88.37,0,0,1,49.63,168Zm101.05,45a142.39,142.39,0,0,0,20.26-45h35.43A88.37,88.37,0,0,1,150.68,213Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#121416] text-base font-normal leading-normal flex-1 truncate">Language</p>
          </div>
          <div class="shrink-0"><p class="text-[#121416] text-base font-normal leading-normal">English</p></div>
        </div>
        <div class="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
          <div class="flex items-center gap-4">
            <div class="text-[#121416] flex items-center justify-center rounded-lg bg-[#f1f2f4] shrink-0 size-10" data-icon="Moon" data-size="24px" data-weight="regular">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M233.54,142.23a8,8,0,0,0-8-2,88.08,88.08,0,0,1-109.8-109.8,8,8,0,0,0-10-10,104.84,104.84,0,0,0-52.91,37A104,104,0,0,0,136,224a103.09,103.09,0,0,0,62.52-20.88,104.84,104.84,0,0,0,37-52.91A8,8,0,0,0,233.54,142.23ZM188.9,190.34A88,88,0,0,1,65.66,67.11a89,89,0,0,1,31.4-26A106,106,0,0,0,96,56,104.11,104.11,0,0,0,200,160a106,106,0,0,0,14.92-1.06A89,89,0,0,1,188.9,190.34Z"
                ></path>
              </svg>
            </div>
            <p class="text-[#121416] text-base font-normal leading-normal flex-1 truncate">Theme</p>
          </div>
          <div class="shrink-0"><p class="text-[#121416] text-base font-normal leading-normal">Light</p></div>
        </div>
      </div>
      <div>
        <div class="flex px-4 py-3">
          <button
            class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 flex-1 bg-[#f1f2f4] text-[#121416] text-sm font-bold leading-normal tracking-[0.015em]"
          >
            <span class="truncate">Log Out</span>
          </button>
        </div>
        <div class="h-5 bg-white"></div>
      </div>
    </div>
  </body>
</html>
