import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { Chapter, chapterService } from '@/services/chapterService';
import { Course, courseService } from '@/services/courseService';
import { Question } from '@/services/questionService';

interface QuestionDetailsProps {
  question: Question;
}

export default function QuestionDetails({ question }: QuestionDetailsProps) {
  const [chapter, setChapter] = useState<Chapter | null>(null);
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDetails = async () => {
      try {
        const chapterData = await chapterService.getChapter(question.chapter_id);
        setChapter(chapterData);
        if (chapterData) {
          const courseData = await courseService.getCourse(chapterData.course_id.toString());
          setCourse(courseData);
        }
      } catch (err) {
        setError('Failed to fetch question details.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchDetails();
  }, [question]);

  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="small" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ThemedText style={styles.questionText}>{question.question_text}</ThemedText>
      <ThemedText style={styles.explanationText}>{question.explanation}</ThemedText>
      {chapter && <ThemedText>Chapter: {chapter.name}</ThemedText>}
      {course && <ThemedText>Course: {course.name}</ThemedText>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 10,
    padding: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    direction: 'rtl',
  },
  questionText: {
    fontWeight: 'bold',
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  explanationText: {
    marginTop: 5,
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
    textAlign: 'center',
  },
});
