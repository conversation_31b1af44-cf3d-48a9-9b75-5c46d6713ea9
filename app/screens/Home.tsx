// import { Redirect } from 'expo-router';

// export default function RootIndex() {
//   return <Redirect href="/(drawer)" />;
// }
import { Image } from "expo-image";
import { Platform, StyleSheet, Button } from "react-native";
import { useRouter } from "expo-router";
import { rtlStyle } from "@/utils/rtl";

import { HelloWave } from "@/components/HelloWave";
import ParallaxScrollView from "@/components/ParallaxScrollView";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";

export default function HomeScreen() {
  const router = useRouter();
  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: "#A1CEDC", dark: "#1D3D47" }}
      headerImage={
        <Image
          source={require("@/assets/images/partial-react-logo.png")}
          // source={require("@/assets/images/LOGO-400x400.png")}
          style={styles.headerLogo}
        />
      }
    >
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title">خوش آمدید!</ThemedText>
        <HelloWave />
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">مدیریت آزمون‌ها</ThemedText>
        <Button
          title="ایجاد آزمون جدید"
          onPress={() => router.push("/ExamAdd")}
        />
        <Button
          title="آزمون‌ها"
          onPress={() => router.push("/screens/ExamsList")}
        />
        <ThemedText>
          Edit{" "}
          <ThemedText type="defaultSemiBold">app/(tabs)/index.tsx</ThemedText>{" "}
          to see changes. Press{" "}
          <ThemedText type="defaultSemiBold">
            {Platform.select({
              ios: "cmd + d",
              android: "cmd + m",
              web: "F12",
            })}
          </ThemedText>{" "}
          to open developer tools.
        </ThemedText>
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 2: Explore</ThemedText>
        <ThemedText>
          {`Tap the Explore tab to learn more about what's included in this starter app.`}
        </ThemedText>
      </ThemedView>
      <ThemedView style={styles.stepContainer}>
        <ThemedText type="subtitle">Step 3: Get a fresh start</ThemedText>
        <ThemedText>
          {`When you're ready, run `}
          <ThemedText type="defaultSemiBold">
            npm run reset-project
          </ThemedText>{" "}
          to get a fresh <ThemedText type="defaultSemiBold">app</ThemedText>{" "}
          directory. This will move the current{" "}
          <ThemedText type="defaultSemiBold">app</ThemedText> to{" "}
          <ThemedText type="defaultSemiBold">app-example</ThemedText>.
        </ThemedText>
      </ThemedView>
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
    gap: 8,
    fontFamily: "Vazirmatn",
  },
  stepContainer: {
    gap: 8,
    marginBottom: 8,
  },
  headerLogo: {
    height: 250,
    width: "100%",
    bottom: 0,
    ...rtlStyle.left(0),
    position: "absolute",
  },
});
