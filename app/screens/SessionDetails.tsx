import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Session, sessionService } from '@/services/sessionService';
import { useLocalSearchParams } from 'expo-router';

export default function SessionDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchSession = async () => {
        try {
          const data = await sessionService.getSession(id as string);
          setSession(data);
        } catch (err) {
          setError('خطا در دریافت جزئیات جلسه.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchSession();
    } else {
      setError('شناسه جلسه ارائه نشده است.');
      setLoading(false);
    }
  }, [id]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>در حال بارگذاری جزئیات جلسه...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  if (!session) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText>جلسه یافت نشد.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>جزئیات جلسه</ThemedText>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>شناسه:</ThemedText>
        <ThemedText>{session.id}</ThemedText>
      </ThemedView>
      {/* Add other session properties here based on your schema */}
      {session.createdAt && (
        <ThemedView style={styles.detailItem}>
          <ThemedText style={styles.label}>تاریخ ایجاد:</ThemedText>
          <ThemedText>{new Date(session.createdAt).toLocaleString()}</ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
