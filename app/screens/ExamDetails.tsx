import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, View, FlatList } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Exam, examService } from '@/services/examService';
import { useLocalSearchParams } from 'expo-router';

export default function ExamDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [exam, setExam] = useState<Exam | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchExam = async () => {
        try {
          const data = await examService.getExam(id as string);
          setExam(data);
        } catch (err) {
          setError('Failed to fetch exam details.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchExam();
    } else {
      setError('Exam ID not provided.');
      setLoading(false);
    }
  }, [id]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading exam details...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  if (!exam) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText>Exam not found.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Exam Details</ThemedText>
      <View style={styles.detailItem}>
        <ThemedText style={styles.label}>ID:</ThemedText>
        <ThemedText>{exam.id}</ThemedText>
      </View>
      <View style={styles.detailItem}>
        <ThemedText style={styles.label}>Name:</ThemedText>
        <ThemedText>{exam.name}</ThemedText>
      </View>
      <View style={styles.detailItem}>
        <ThemedText style={styles.label}>Duration:</ThemedText>
        <ThemedText>{exam.duration_minutes} minutes</ThemedText>
      </View>
      <View style={styles.detailItem}>
        <ThemedText style={styles.label}>Instructions:</ThemedText>
        <ThemedText>{exam.instructions}</ThemedText>
      </View>
      <View style={styles.detailItem}>
        <ThemedText style={styles.label}>Major ID:</ThemedText>
        <ThemedText>{exam.major_id}</ThemedText>
      </View>
      {exam.created_at && (
        <View style={styles.detailItem}>
          <ThemedText style={styles.label}>Created At:</ThemedText>
          <ThemedText>{new Date(exam.created_at).toLocaleString()}</ThemedText>
        </View>
      )}
      {exam.updatad_at && (
        <View style={styles.detailItem}>
          <ThemedText style={styles.label}>Updated At:</ThemedText>
          <ThemedText>{new Date(exam.updatad_at).toLocaleString()}</ThemedText>
        </View>
      )}

      <ThemedText style={styles.title}>Questions</ThemedText>
      <FlatList
        data={exam.questions}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <View style={styles.questionContainer}>
            <ThemedText style={styles.questionText}>{item.question_text}</ThemedText>
            <ThemedText style={styles.explanationText}>{item.explanation}</ThemedText>
          </View>
        )}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  questionContainer: {
    marginBottom: 10,
    padding: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
  },
  questionText: {
    fontWeight: 'bold',
  },
  explanationText: {
    marginTop: 5,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
