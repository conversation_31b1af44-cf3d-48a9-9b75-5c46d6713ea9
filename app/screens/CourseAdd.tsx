import React, { useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { courseService } from '@/services/courseService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router } from 'expo-router';

export default function CourseAddScreen() {
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddCourse = async () => {
    if (!name.trim()) {
      Alert.alert('خطای اعتبارسنجی', 'نام درس نمی‌تواند خالی باشد.');
      return;
    }

    setLoading(true);
    try {
      await courseService.createCourse({ name });
      Alert.alert('موفقیت', 'درس با موفقیت اضافه شد!');
      router.back();
    } catch (error) {
      Alert.alert('خطا', 'افزودن درس ناموفق بود. لطفاً دوباره تلاش کنید.');
      console.error('Failed to add course:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>افزودن درس جدید</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="نام درس"
        value={name}
        onChangeText={setName}
        editable={!loading}
      />
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddCourse}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'در حال افزودن...' : 'افزودن درس'}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
