import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from "react-native";
import { router, useNavigation } from "expo-router";
import { Svg, Path } from "react-native-svg";

import { rtlStyle } from "@/utils/rtl";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Colors } from "@/constants/Colors";
import { MenuButton } from "@/components/ui/MenuButton";

// Icon Components
const BackArrowIcon = ({ color = "#1F2937", size = 24 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
  >
    <Path d="M15 18l-6-6 6-6" />
  </Svg>
);

export default function SettingsScreen() {
  const colorScheme = useColorScheme() ?? "light";
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(colorScheme === "dark");
  const [autoSync, setAutoSync] = useState(true);

  const handleBack = () => {
    router.back();
  };

  const handleClearCache = () => {
    Alert.alert(
      "پاک کردن حافظه موقت",
      "آیا مطمئن هستید که می‌خواهید حافظه موقت را پاک کنید؟",
      [
        { text: "لغو", style: "cancel" },
        {
          text: "پاک کردن",
          style: "destructive",
          onPress: () => Alert.alert("موفقیت", "حافظه موقت پاک شد"),
        },
      ]
    );
  };

  const SettingItem = ({
    title,
    subtitle,
    value,
    onValueChange,
    type = "switch",
  }: {
    title: string;
    subtitle?: string;
    value?: boolean;
    onValueChange?: (value: boolean) => void;
    type?: "switch" | "button";
  }) => (
    <View
      style={[
        styles.settingItem,
        { borderBottomColor: Colors[colorScheme].tabIconDefault + "30" },
      ]}
    >
      <View style={styles.settingContent}>
        <Text
          style={[styles.settingTitle, { color: Colors[colorScheme].text }]}
        >
          {title}
        </Text>
        {subtitle && (
          <Text
            style={[
              styles.settingSubtitle,
              { color: Colors[colorScheme].tabIconDefault },
            ]}
          >
            {subtitle}
          </Text>
        )}
      </View>
      {type === "switch" && (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: "#767577", true: Colors[colorScheme].tint }}
          thumbColor={value ? "#FFFFFF" : "#f4f3f4"}
        />
      )}
    </View>
  );

  return (
    <SafeAreaView
      style={[
        styles.container,
        { backgroundColor: Colors[colorScheme].background },
      ]}
    >
      {/* Header */}
      <View
        style={[
          styles.header,
          { borderBottomColor: Colors[colorScheme].tabIconDefault + "30" },
        ]}
      >
        <TouchableOpacity style={styles.headerButton} onPress={handleBack}>
          <BackArrowIcon color={Colors[colorScheme].text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: Colors[colorScheme].text }]}>
          تنظیمات
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* General Settings */}
        <View style={styles.section}>
          <Text
            style={[styles.sectionTitle, { color: Colors[colorScheme].text }]}
          >
            عمومی
          </Text>

          <SettingItem
            title="اعلان‌ها"
            subtitle="دریافت اعلان‌های برنامه"
            value={notifications}
            onValueChange={setNotifications}
          />

          <SettingItem
            title="حالت تاریک"
            subtitle="استفاده از تم تاریک"
            value={darkMode}
            onValueChange={setDarkMode}
          />

          <SettingItem
            title="همگام‌سازی خودکار"
            subtitle="همگام‌سازی خودکار داده‌ها"
            value={autoSync}
            onValueChange={setAutoSync}
          />
        </View>

        {/* Data & Storage */}
        <View style={styles.section}>
          <Text
            style={[styles.sectionTitle, { color: Colors[colorScheme].text }]}
          >
            داده‌ها و ذخیره‌سازی
          </Text>

          <TouchableOpacity
            style={[
              styles.settingItem,
              { borderBottomColor: Colors[colorScheme].tabIconDefault + "30" },
            ]}
            onPress={handleClearCache}
          >
            <View style={styles.settingContent}>
              <Text
                style={[
                  styles.settingTitle,
                  { color: Colors[colorScheme].text },
                ]}
              >
                پاک کردن حافظه موقت
              </Text>
              <Text
                style={[
                  styles.settingSubtitle,
                  { color: Colors[colorScheme].tabIconDefault },
                ]}
              >
                حذف فایل‌های موقت و کش
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* About */}
        <View style={styles.section}>
          <Text
            style={[styles.sectionTitle, { color: Colors[colorScheme].text }]}
          >
            درباره
          </Text>

          <View
            style={[styles.settingItem, { borderBottomColor: "transparent" }]}
          >
            <View style={styles.settingContent}>
              <Text
                style={[
                  styles.settingTitle,
                  { color: Colors[colorScheme].text },
                ]}
              >
                نسخه برنامه
              </Text>
              <Text
                style={[
                  styles.settingSubtitle,
                  { color: Colors[colorScheme].tabIconDefault },
                ]}
              >
                ۱.۰.۰
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    fontFamily: "Vazirmatn",
    textAlign: "center",
  },
  content: {
    flex: 1,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    fontFamily: "Vazirmatn",
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  settingItem: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: "500",
    fontFamily: "Vazirmatn",
    textAlign: rtlStyle.textAlign.start,
  },
  settingSubtitle: {
    fontSize: 14,
    marginTop: 4,
    textAlign: rtlStyle.textAlign.start,
  },
});
