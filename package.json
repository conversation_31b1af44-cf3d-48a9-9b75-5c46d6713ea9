{"name": "azmoon", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --port=8082 --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native-picker/picker": "^2.11.1", "@react-native/babel-preset": "0.80.0", "@react-native/eslint-config": "0.80.0", "@react-native/metro-config": "0.80.0", "@react-native/new-app-screen": "^0.80.0", "@react-native/typescript-config": "0.80.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.4.1", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "expo": "53.0.15", "expo-blur": "~14.1.5", "expo-checkbox": "^4.1.4", "expo-constants": "~17.1.6", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.3.1", "expo-linking": "~7.1.6", "expo-router": "~5.1.2", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jest": "^29.7.0", "jest-expo": "~53.0.8", "react-native-svg-transformer": "^1.5.1", "react-test-renderer": "^19.1.0", "typescript": "~5.8.3"}, "private": true}