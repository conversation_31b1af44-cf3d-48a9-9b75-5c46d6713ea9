/* Generated by script */
@font-face {
  font-family: Vazirmatn RD UI;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-Thin.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-ExtraLight.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-ExtraBold.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}
