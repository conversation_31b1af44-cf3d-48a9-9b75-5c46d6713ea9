/* Generated by script */
@font-face {
  font-family: Vazirmatn RD UI NL;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-NL-Thin.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI NL;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-NL-ExtraLight.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI NL;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-NL-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI NL;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-NL-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI NL;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-NL-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI NL;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-NL-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI NL;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-NL-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI NL;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-NL-ExtraBold.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn RD UI NL;
  src: url('fonts/webfonts/Vazirmatn-RD-UI-NL-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}
