/* Generated by script */
@font-face {
  font-family: Vazirmatn FD NL;
  src: url('fonts/webfonts/Vazirmatn-FD-NL-Thin.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn FD NL;
  src: url('fonts/webfonts/Vazirmatn-FD-NL-ExtraLight.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn FD NL;
  src: url('fonts/webfonts/Vazirmatn-FD-NL-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn FD NL;
  src: url('fonts/webfonts/Vazirmatn-FD-NL-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn FD NL;
  src: url('fonts/webfonts/Vazirmatn-FD-NL-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn FD NL;
  src: url('fonts/webfonts/Vazirmatn-FD-NL-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn FD NL;
  src: url('fonts/webfonts/Vazirmatn-FD-NL-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn FD NL;
  src: url('fonts/webfonts/Vazirmatn-FD-NL-ExtraBold.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn FD NL;
  src: url('fonts/webfonts/Vazirmatn-FD-NL-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}
