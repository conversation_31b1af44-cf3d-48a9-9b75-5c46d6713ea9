/* Generated by script */
@font-face {
  font-family: Vazirmatn NL;
  src: url('fonts/webfonts/Vazirmatn-NL-Thin.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn NL;
  src: url('fonts/webfonts/Vazirmatn-NL-ExtraLight.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn NL;
  src: url('fonts/webfonts/Vazirmatn-NL-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn NL;
  src: url('fonts/webfonts/Vazirmatn-NL-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn NL;
  src: url('fonts/webfonts/Vazirmatn-NL-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn NL;
  src: url('fonts/webfonts/Vazirmatn-NL-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn NL;
  src: url('fonts/webfonts/Vazirmatn-NL-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn NL;
  src: url('fonts/webfonts/Vazirmatn-NL-ExtraBold.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Vazirmatn NL;
  src: url('fonts/webfonts/Vazirmatn-NL-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}
