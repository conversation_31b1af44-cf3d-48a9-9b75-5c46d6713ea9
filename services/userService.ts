import { api } from './api';

// Define types
export interface User {
  id: string;
  email: string;
  mobile: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  role: string;
}

export interface CreateUserDto extends Omit<User, 'id'> {}

export interface UpdateUserDto extends Partial<Omit<User, 'id'>> {}

// User service for handling user-related API calls
export const userService = {
  // Get all users
  async getUsers(): Promise<User[]> {
    const response = await api.get<User[]>('/users');
    return response.data;
  },

  // Get a specific user
  async getUser(id: string): Promise<User> {
    const response = await api.get<User>(`/users/${id}`);
    return response.data;
  },

  // Create a new user
  async createUser(userData: CreateUserDto): Promise<User> {
    const response = await api.post<User>('/users', userData);
    return response.data;
  },

  // Update a user
  async updateUser(id: string, userData: UpdateUserDto): Promise<User> {
    const response = await api.patch<User>(`/users/${id}`, userData);
    return response.data;
  },

  // Delete a user
  async deleteUser(id: string): Promise<void> {
    await api.delete(`/users/${id}`);
  }
};
