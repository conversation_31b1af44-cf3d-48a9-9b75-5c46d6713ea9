import { api } from './api';

// Define types
export interface Question {
  id: string;
  question_text: string;
  chapter_id: string;
  explanation: string;
  difficulty_level: number;
}

// Question service for handling question-related API calls
export const questionService = {
  // Get all questions
  async getQuestions(): Promise<Question[]> {
    const response = await api.get<Question[]>('/questions');
    return response.data;
  },

  // Get a specific question
  async getQuestion(id: string): Promise<Question> {
    const response = await api.get<Question>(`/questions/${id}`);
    return response.data;
  },

  // Create a new question
  async createQuestion(questionData: Omit<Question, 'id'>): Promise<Question> {
    const response = await api.post<Question>('/questions', questionData);
    return response.data;
  },

  // Update a question
  async updateQuestion(id: string, questionData: Partial<Question>): Promise<Question> {
    const response = await api.put<Question>(`/questions/${id}`, questionData);
    return response.data;
  },

  // Delete a question
  async deleteQuestion(id: string): Promise<void> {
    await api.delete(`/questions/${id}`);
  }
};
