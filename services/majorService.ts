import { api } from './api';

// Define types
export interface Major {
  id: string;
  name: string;
  description: string;
  createdAt?: string;
}

// Major service for handling major-related API calls
export const majorService = {
  // Get all majors
  async getMajors(): Promise<Major[]> {
    const response = await api.get<Major[]>('/majors');
    return response.data;
  },

  // Get a specific major
  async getMajor(id: string): Promise<Major> {
    const response = await api.get<Major>(`/majors/${id}`);
    return response.data;
  },

  // Create a new major
  async createMajor(majorData: Omit<Major, 'id'>): Promise<Major> {
    const response = await api.post<Major>('/majors', majorData);
    return response.data;
  },

  // Update a major
  async updateMajor(id: string, majorData: Partial<Major>): Promise<Major> {
    const response = await api.put<Major>(`/majors/${id}`, majorData);
    return response.data;
  },

  // Delete a major
  async deleteMajor(id: string): Promise<void> {
    await api.delete(`/majors/${id}`);
  }
};