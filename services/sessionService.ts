import { api } from './api';

// Define types
export interface Session {
  id: string;
  userId: string;
  examId: string;
  score: number;
  createdAt?: string;
}

// Session service for handling session-related API calls
export const sessionService = {
  // Get all sessions
  async getSessions(): Promise<Session[]> {
    const response = await api.get<Session[]>('/sessions');
    return response.data;
  },

  // Get a specific session
  async getSession(id: string): Promise<Session> {
    const response = await api.get<Session>(`/sessions/${id}`);
    return response.data;
  },

  // Create a new session
  async createSession(sessionData: Omit<Session, 'id'>): Promise<Session> {
    const response = await api.post<Session>('/sessions', sessionData);
    return response.data;
  },

  // Update a session
  async updateSession(id: string, sessionData: Partial<Session>): Promise<Session> {
    const response = await api.patch<Session>(`/sessions/${id}`, sessionData);
    return response.data;
  },

  // Delete a session
  async deleteSession(id: string): Promise<void> {
    await api.delete(`/sessions/${id}`);
  }
};
