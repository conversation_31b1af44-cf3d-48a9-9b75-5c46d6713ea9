import { api } from './api';

// Define types
export interface MajorCourse {
  majorId: string;
  courseId: string;
  createdAt?: string;
}

// MajorCourse service for handling major-course-related API calls
export const majorCourseService = {
  // Get all major-courses
  async getMajorCourses(): Promise<MajorCourse[]> {
    const response = await api.get<MajorCourse[]>('/major-courses');
    return response.data;
  },

  // Get a specific major-course by majorId and courseId
  async getMajorCourse(majorId: string, courseId: string): Promise<MajorCourse> {
    const response = await api.get<MajorCourse>(`/major-courses/${majorId}/${courseId}`);
    return response.data;
  },

  // Create a new major-course
  async createMajorCourse(majorCourseData: MajorCourse): Promise<MajorCourse> {
    const response = await api.post<MajorCourse>('/major-courses', majorCourseData);
    return response.data;
  },

  // Update a major-course
  async updateMajorCourse(majorId: string, courseId: string, majorCourseData: Partial<MajorCourse>): Promise<MajorCourse> {
    const response = await api.patch<MajorCourse>(`/major-courses/${majorId}/${courseId}`, majorCourseData);
    return response.data;
  },

  // Delete a major-course
  async deleteMajorCourse(majorId: string, courseId: string): Promise<void> {
    await api.delete(`/major-courses/${majorId}/${courseId}`);
  }
};
