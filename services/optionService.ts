import { api } from './api';

// Define types
export interface Option {
  id: string;
  text: string;
  questionId: string;
  isCorrect: boolean;
  createdAt?: string;
}

// Option service for handling option-related API calls
export const optionService = {
  // Get all options
  async getOptions(questionId = null): Promise<Option[]> {
    const path = questionId ? `/questions/${questionId}` : "/options";
    const response = await api.get<Option[]>(path);
    return response.data;
  },

  // Get a specific option
  async getOption(id: string): Promise<Option> {
    const response = await api.get<Option>(`/options/${id}`);
    return response.data;
  },

  // Create a new option
  async createOption(optionData: Omit<Option, 'id'>): Promise<Option> {
    const response = await api.post<Option>('/options', optionData);
    return response.data;
  },

  // Update an option
  async updateOption(id: string, optionData: Partial<Option>): Promise<Option> {
    const response = await api.patch<Option>(`/options/${id}`, optionData);
    return response.data;
  },

  // Delete an option
  async deleteOption(id: string): Promise<void> {
    await api.delete(`/options/${id}`);
  }
};
