import { api } from './api';
import { Question } from './questionService';

// Define types
export interface Exam {
  id: string;
  name: string;
  duration_minutes: number;
  instructions: string;
  major_id: string;
  questions: Question[];
  created_at?: string;
  updatad_at?: string;
}

// Exam service for handling exam-related API calls
export const examService = {
  // Get all exams
  async getExams(): Promise<Exam[]> {
    const response = await api.get<Exam[]>('/exams');
    return response.data;
  },

  // Get a specific exam
  async getExam(id: string): Promise<Exam> {
    const response = await api.get<Exam>(`/exams/${id}`);
    return response.data;
  },

  // Create a new exam
  async createExam(examData: Omit<Exam, 'id' | 'createdAt'>): Promise<Exam> {
    const response = await api.post<Exam>('/exams', examData);
    return response.data;
  },

  // Update a exam
  async updateExam(id: string, examData: Partial<Exam>): Promise<Exam> {
    const response = await api.put<Exam>(`/exams/${id}`, examData);
    return response.data;
  },

  // Delete a exam
  async deleteExam(id: string): Promise<void> {
    await api.delete(`/exams/${id}`);
  }
};
