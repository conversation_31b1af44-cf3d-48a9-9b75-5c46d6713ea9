import { api } from './api';

// Define types
export interface Course {
  id: string;
  name: string;
  createdAt?: string;
}

// Course service for handling course-related API calls
export const courseService = {
  // Get all courses
  async getCourses(): Promise<Course[]> {
    const response = await api.get<Course[]>('/courses');
    return response.data;
  },

  // Get a specific course
  async getCourse(id: string): Promise<Course> {
    const response = await api.get<Course>(`/courses/${id}`);
    return response.data;
  },

  // Create a new course
  async createCourse(courseData: Omit<Course, 'id'>): Promise<Course> {
    const response = await api.post<Course>('/courses', courseData);
    return response.data;
  },

  // Update a course
  async updateCourse(id: string, courseData: Partial<Course>): Promise<Course> {
    const response = await api.patch<Course>(`/courses/${id}`, courseData);
    return response.data;
  },

  // Delete a course
  async deleteCourse(id: string): Promise<void> {
    await api.delete(`/courses/${id}`);
  }
};
